import 'dart:async';
import 'dart:io';

import 'package:flutter/material.dart';
import 'package:flutter_blue_plus/flutter_blue_plus.dart';

import 'device_screen.dart';
import '../utils/snackbar.dart';
import '../widgets/system_device_tile.dart';
import '../widgets/scan_result_tile.dart';
import '../utils/extra.dart';

class ScanScreen extends StatefulWidget {
  const ScanScreen({Key? key}) : super(key: key);

  @override
  State<ScanScreen> createState() => _ScanScreenState();
}

class _ScanScreenState extends State<ScanScreen> {
  List<BluetoothDevice> _systemDevices = [];
  List<BluetoothDevice> _bondedDevices = [];
  List<ScanResult> _scanResults = [];
  bool _isScanning = false;
  late StreamSubscription<List<ScanResult>> _scanResultsSubscription;
  late StreamSubscription<bool> _isScanningSubscription;

  @override
  void initState() {
    super.initState();

    _scanResultsSubscription = FlutterBluePlus.scanResults.listen(
      (results) {
        _scanResults = results;
        if (mounted) {
          setState(() {});
        }
      },
      onError: (e) {
        Snackbar.show(ABC.b, prettyException("Scan Error:", e), success: false);
      },
    );

    _isScanningSubscription = FlutterBluePlus.isScanning.listen((state) {
      _isScanning = state;
      if (mounted) {
        setState(() {});
      }
    });

    // _autoConnectBondedDevices();
  }

  @override
  void dispose() {
    _scanResultsSubscription.cancel();
    _isScanningSubscription.cancel();
    super.dispose();
  }

  Future<void> _autoConnectBondedDevices() async {
    try {
      _bondedDevices = await FlutterBluePlus.bondedDevices;

      if (_bondedDevices.isNotEmpty) {
        for (BluetoothDevice device in _bondedDevices) {
          if (device.platformName.contains("Smart Key")) {
            onConnectPressed(device);
            break; // Connect to the first matching device found
          }
        }
      }
    } catch (e) {
      Snackbar.show(
        ABC.b,
        prettyException("Auto-connect Error:", e),
        success: false,
      );
    }
  }

  Future onScanPressed() async {
    try {
      // `withServices` is required on iOS for privacy purposes, ignored on android.
      var withServices = [Guid("180f")]; // Battery Level Service
      _systemDevices = await FlutterBluePlus.systemDevices(List<Guid>.empty());
    } catch (e, backtrace) {
      Snackbar.show(
        ABC.b,
        prettyException("System Devices Error:", e),
        success: false,
      );
      print(e);
      print("backtrace: $backtrace");
    }
    try {
      await FlutterBluePlus.startScan(
        timeout: const Duration(seconds: 15),
        // withKeywords: ["Smart Key"],
        // withMsd: [MsdFilter(0xFF)],
        // webOptionalServices: [
        //   Guid("180f"), // battery
        //   Guid("1800"), // generic access
        //   Guid("6e400001-b5a3-f393-e0a9-e50e24dcca9e"), // Nordic UART
        // ],
      );
    } catch (e, backtrace) {
      Snackbar.show(
        ABC.b,
        prettyException("Start Scan Error:", e),
        success: false,
      );
      print(e);
      print("backtrace: $backtrace");
    }
    if (mounted) {
      setState(() {});
    }
  }

  Future onStopPressed() async {
    try {
      FlutterBluePlus.stopScan();
    } catch (e, backtrace) {
      Snackbar.show(
        ABC.b,
        prettyException("Stop Scan Error:", e),
        success: false,
      );
      print(e);
      print("backtrace: $backtrace");
    }
  }

  void onConnectPressed(BluetoothDevice device) {
    device.connectAndUpdateStream().then((_) async {
      // Check if device is already bonded
      bool isBonded = false;
      if (Platform.isAndroid) {
        try {
          // Get current bonded devices to check
          List<BluetoothDevice> bondedDevices = await FlutterBluePlus.bondedDevices;
          isBonded = bondedDevices.any((d) => d.remoteId == device.remoteId);

          if (!isBonded) {
            // Show bonding in progress indicator
            Snackbar.show(ABC.c, "Bonding with device...", success: true);

            // Create bond if not already bonded
            await device.createBond();

            // Verify bond was created
            bondedDevices = await FlutterBluePlus.bondedDevices;
            isBonded = bondedDevices.any((d) => d.remoteId == device.remoteId);

            if (isBonded) {
              Snackbar.show(ABC.c, "Device bonded successfully", success: true);
              // Now navigate to device screen
              _navigateToDeviceScreen(device);
            } else {
              Snackbar.show(ABC.c, "Bonding failed, please try again", success: false);
            }
          } else {
            // Already bonded, proceed to device screen
            _navigateToDeviceScreen(device);
          }
        } catch (e) {
          Snackbar.show(
            ABC.c,
            prettyException("Bonding Error:", e),
            success: false,
          );
        }
      } else {
        // For iOS, bonding happens automatically if needed
        _navigateToDeviceScreen(device);
      }
    }).catchError((e) {
      Snackbar.show(
        ABC.c,
        prettyException("Connect Error:", e),
        success: false,
      );
    });
  }

  // Helper method to navigate to device screen
  void _navigateToDeviceScreen(BluetoothDevice device) {
    MaterialPageRoute route = MaterialPageRoute(
      builder: (context) => DeviceScreen(device: device),
      settings: RouteSettings(name: '/DeviceScreen'),
    );
    Navigator.of(context).push(route);
  }


  Future onRefresh() {
    if (_isScanning == false) {
      FlutterBluePlus.startScan(
        timeout: const Duration(seconds: 15),
        androidUsesFineLocation: true,
      );
    }
    if (mounted) {
      setState(() {});
    }
    return Future.delayed(Duration(milliseconds: 500));
  }

  Widget buildScanButton(BuildContext context) {
    if (FlutterBluePlus.isScanningNow) {
      return FloatingActionButton(
        onPressed: onStopPressed,
        backgroundColor: Colors.red,
        child: const Icon(Icons.stop),
      );
    } else {
      return FloatingActionButton(
        onPressed: onScanPressed,
        child: const Text("SCAN"),
      );
    }
  }

  List<Widget> _buildSystemDeviceTiles(BuildContext context) {
    return _systemDevices
        .map(
          (d) => SystemDeviceTile(
            device: d,
            onOpen:
                () => Navigator.of(context).push(
                  MaterialPageRoute(
                    builder: (context) => DeviceScreen(device: d),
                    settings: RouteSettings(name: '/DeviceScreen'),
                  ),
                ),
            onConnect: () => onConnectPressed(d),
          ),
        )
        .toList();
  }

  List<Widget> _buildScanResultTiles(BuildContext context) {
    return _scanResults
        .map(
          (r) => ScanResultTile(
            result: r,
            onTap: () => onConnectPressed(r.device),
          ),
        )
        .toList();
  }

  @override
  Widget build(BuildContext context) {
    return ScaffoldMessenger(
      key: Snackbar.snackBarKeyB,
      child: Scaffold(
        appBar: AppBar(title: const Text('Find Devices')),
        body: RefreshIndicator(
          onRefresh: onRefresh,
          child: ListView(
            children: <Widget>[
              ..._buildSystemDeviceTiles(context),
              ..._buildScanResultTiles(context),
            ],
          ),
        ),
        floatingActionButton: buildScanButton(context),
      ),
    );
  }
}
