import 'dart:async';

import 'package:flutter/material.dart';
import 'package:flutter_blue_plus/flutter_blue_plus.dart';

import 'device_screen.dart';
import '../utils/snackbar.dart';
import '../utils/extra.dart';
import '../widgets/system_device_tile.dart';

class PairedScreen extends StatefulWidget {
  const PairedScreen({Key? key}) : super(key: key);

  @override
  State<PairedScreen> createState() => _PairedScreenState();
}

class _PairedScreenState extends State<PairedScreen> {
  List<BluetoothDevice> _pairedDevices = [];

  @override
  void initState() {
    super.initState();
    _loadPairedDevices();
  }

  Future<void> _loadPairedDevices() async {
    try {
      // On iOS, we need to specify services for privacy
      // var withServices = [Guid("180f")]; // Battery Service UUID
      _pairedDevices = await FlutterBluePlus.systemDevices(List<Guid>.empty());
      if (mounted) {
        setState(() {});
      }
    } catch (e, backtrace) {
      Snackbar.show(ABC.b, prettyException("Load Paired Devices Error:", e), success: false);
      print(e);
      print("backtrace: $backtrace");
    }
  }

  void onConnectPressed(BluetoothDevice device) {
    device.connectAndUpdateStream().catchError((e) {
      Snackbar.show(ABC.c, prettyException("Connect Error:", e), success: false);
    });
    MaterialPageRoute route = MaterialPageRoute(
      builder: (context) => DeviceScreen(device: device),
      settings: RouteSettings(name: '/DeviceScreen'),
    );
    Navigator.of(context).push(route);
  }

  Future<void> onRefresh() async {
    await _loadPairedDevices();
    return Future.delayed(Duration(milliseconds: 500));
  }

  List<Widget> _buildPairedDeviceTiles(BuildContext context) {
    return _pairedDevices
        .map(
          (d) => SystemDeviceTile(
            device: d,
            onOpen: () => Navigator.of(context).push(
              MaterialPageRoute(
                builder: (context) => DeviceScreen(device: d),
                settings: RouteSettings(name: '/DeviceScreen'),
              ),
            ),
            onConnect: () => onConnectPressed(d),
          ),
        )
        .toList();
  }

  @override
  Widget build(BuildContext context) {
    return ScaffoldMessenger(
      key: Snackbar.snackBarKeyB,
      child: Scaffold(
        appBar: AppBar(
          title: const Text('Paired Devices'),
        ),
        body: RefreshIndicator(
          onRefresh: onRefresh,
          child: ListView(
            children: <Widget>[
              ..._buildPairedDeviceTiles(context),
            ],
          ),
        ),
      ),
    );
  }
}