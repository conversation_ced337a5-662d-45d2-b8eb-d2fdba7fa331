import 'dart:async';
import 'dart:math';

import 'package:ble/utils/extra.dart';
import 'package:flutter/material.dart';
import 'package:flutter_blue_plus/flutter_blue_plus.dart';
import 'device_screen.dart';
import 'scan_screen.dart';

class HomeScreen extends StatefulWidget {
  const HomeScreen({super.key});

  @override
  State<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen> with WidgetsBindingObserver {
  List<BluetoothDevice> _bondedDevices = [];
  Timer? _backgroundConnectTimer;

  @override
  void initState() {
    super.initState();
    _getBondedDevices();
    WidgetsBinding.instance.addObserver(this);
    _startBackgroundConnect();
  }

  @override
  void dispose() {
    _backgroundConnectTimer?.cancel();
    WidgetsBinding.instance.removeObserver(this);
    super.dispose();
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    if (state == AppLifecycleState.resumed) {
      _getBondedDevices();
    }
  }

  void _startBackgroundConnect() {
    // Try to connect every 30 seconds
    _backgroundConnectTimer = Timer.periodic(const Duration(seconds: 30), (_) {
      _tryConnectToDevices();
    });
  }

  Future<void> _tryConnectToDevices() async {
    if (_bondedDevices.isEmpty) {
      await _getBondedDevices();
    }

    for (final device in _bondedDevices) {
      if (device.connectionState != BluetoothConnectionState.connected) {
        try {
          await device.connectAndUpdateStream();
          print('Auto-connected to ${device.platformName}');
        } catch (e) {
          print('Failed to auto-connect to ${device.platformName}: $e');
        }
      }
    }
  }

  Future<void> _getBondedDevices() async {
    try {
      final devices = await FlutterBluePlus.bondedDevices;
      setState(() {
        _bondedDevices = devices;
      });
    } catch (e) {
      print('Error getting bonded devices: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('Logo', style: TextStyle(color: Colors.black)),
        centerTitle: true,
        backgroundColor: Colors.white,
        elevation: 2,
        actions: [
          IconButton(
            icon: Icon(Icons.add, color: Colors.black),
            onPressed: () {
              Navigator.push(
                context,
                MaterialPageRoute(builder: (context) => ScanScreen()),
              );
            },
          ),
        ],
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          children: [
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Text(
                  'Hello!!!',
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                ),
              ),
            ),
            const SizedBox(height: 16),
            Text(
              'Danh sach thiet bi',
              style: TextStyle(fontSize: 16, fontWeight: FontWeight.w500),
            ),
            const SizedBox(height: 16),
            Expanded(
              child: GridView.builder(
                gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                  crossAxisCount: 2,
                  mainAxisSpacing: 16,
                  crossAxisSpacing: 16,
                  childAspectRatio: 0.8,
                ),
                itemCount: _bondedDevices.length,
                itemBuilder: (context, index) {
                  final device = _bondedDevices[index];
                  return DeviceCard(device: device);
                },
              ),
            ),
          ],
        ),
      ),
    );
  }
}

class DeviceCard extends StatelessWidget {
  final BluetoothDevice device;

  const DeviceCard({super.key, required this.device});

  void _connectToDevice(BuildContext context) {
    device.connectAndUpdateStream().catchError((e) {
      ScaffoldMessenger.of(
        context,
      ).showSnackBar(SnackBar(content: Text('Connection error: $e')));
    });
    Navigator.push(
      context,
      MaterialPageRoute(builder: (context) => DeviceScreen(device: device)),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        Expanded(
          child: GestureDetector(
            onTap: () => _connectToDevice(context),
            child: Container(
              width: double.infinity,
              decoration: BoxDecoration(
                border: Border.all(color: Colors.black),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Center(
                child: Text(
                  device.platformName,
                  style: TextStyle(fontSize: 16),
                  textAlign: TextAlign.center,
                ),
              ),
            ),
          ),
        ),
        const SizedBox(height: 8),
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceEvenly,
          children: [
            CircleButton(
              label: 'Reset',
              onPressed: () {
                _resetDevicePasscode(context, device);
              },
            ),
            CircleButton(
              label: 'Lock',
              onPressed: () {
                _lockDevice(context, device);
              },
            ),
            CircleButton(
              label: 'Passcode',
              onPressed: () {
                _changeDevicePasscode(context, device);
              },
            ),
            CircleButton(
              label: 'OTA',
              onPressed: () {
                _goToOTA(context, device);
              },
            ),
          ],
        ),
      ],
    );
  }
}

class CircleButton extends StatelessWidget {
  final String label;
  final VoidCallback onPressed;

  const CircleButton({super.key, required this.label, required this.onPressed});

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        Ink(
          decoration: const ShapeDecoration(
            color: Colors.transparent,
            shape: CircleBorder(side: BorderSide(color: Colors.black)),
          ),
          child: IconButton(
            icon: Icon(Icons.circle, size: 0), // invisible icon
            onPressed: onPressed,
          ),
        ),
        const SizedBox(height: 4),
        Text(label, style: TextStyle(fontSize: 10)),
      ],
    );
  }
}

void _resetDevicePasscode(BuildContext context, BluetoothDevice device) async {
  try {
    // Show confirmation dialog
    bool confirm = await showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('Reset Passcode'),
        content: Text('Are you sure you want to reset the device passcode to default?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context, false),
            child: Text('Cancel'),
          ),
          TextButton(
            onPressed: () => Navigator.pop(context, true),
            style: TextButton.styleFrom(foregroundColor: Colors.red),
            child: Text('Reset'),
          ),
        ],
      ),
    ) ?? false;

    if (!confirm) return;

    // Connect to device if not already connected
    if (device.connectionState != BluetoothConnectionState.connected) {
      await device.connectAndUpdateStream();
    }

    // Discover services
    List<BluetoothService> services = await device.discoverServices();

    // Find the control service and characteristic
    // Note: Replace these UUIDs with your actual service and characteristic UUIDs
    final serviceUuid = Guid("ffe0");
    // list characteristic ffe1 ffe2 ffe3 ffe4 ffe5
    final characteristicUuid = Guid("ffe1"); // TX characteristic

    BluetoothService? service = services.firstWhere(
      (s) => s.uuid == serviceUuid,
      orElse: () => throw Exception("Control service not found"),
    );

    BluetoothCharacteristic? characteristic = service.characteristics.firstWhere(
      (c) => c.uuid == characteristicUuid,
      orElse: () => throw Exception("Control characteristic not found"),
    );

    // Send reset command
    // The actual command format will depend on your device protocol
    List<int> resetCommand = [0x01];
    await characteristic.write(resetCommand);

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text('Device passcode reset to default'))
    );
  } catch (e) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text('Reset failed: $e'))
    );
    print('Error resetting device: $e');
  }
}

void _lockDevice(BuildContext context, BluetoothDevice device) async {
  try {
    // Show confirmation dialog
    bool confirm = await showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('Lock Device'),
        content: Text('Are you sure you want to lock this device?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context, false),
            child: Text('Cancel'),
          ),
          TextButton(
            onPressed: () => Navigator.pop(context, true),
            style: TextButton.styleFrom(foregroundColor: Colors.blue),
            child: Text('Lock'),
          ),
        ],
      ),
    ) ?? false;

    if (!confirm) return;

    // Connect to device if not already connected
    if (device.connectionState != BluetoothConnectionState.connected) {
      await device.connectAndUpdateStream();
    }

    // Discover services
    List<BluetoothService> services = await device.discoverServices();

    // Find the control service and characteristic
    final serviceUuid = Guid("ffe0");
    final characteristicUuid = Guid("ffe2");

    BluetoothService? service = services.firstWhere(
      (s) => s.uuid == serviceUuid,
      orElse: () => throw Exception("Control service not found"),
    );

    BluetoothCharacteristic? characteristic = service.characteristics.firstWhere(
      (c) => c.uuid == characteristicUuid,
      orElse: () => throw Exception("Control characteristic not found"),
    );

    // Send lock command
    List<int> lockCommand = [0x01];
    await characteristic.write(lockCommand);

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text('Device locked successfully'))
    );
  } catch (e) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text('Lock failed: $e'))
    );
    print('Error locking device: $e');
  }
}

void _changeDevicePasscode(BuildContext context, BluetoothDevice device) async {
  // Controller for the passcode input
  final TextEditingController passcodeController = TextEditingController();

  try {
    // Show dialog to input new passcode
    bool? result = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('Change Passcode'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text('Enter a new 6-digit passcode:'),
            TextField(
              controller: passcodeController,
              keyboardType: TextInputType.number,
              maxLength: 6,
              decoration: InputDecoration(
                hintText: '123456',
                counterText: '',
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context, false),
            child: Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              // Validate input is 6 digits
              String input = passcodeController.text;
              if (input.length == 6 && int.tryParse(input) != null) {
                Navigator.pop(context, true);
              } else {
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(content: Text('Please enter a valid 6-digit passcode'))
                );
              }
            },
            style: TextButton.styleFrom(foregroundColor: Colors.blue),
            child: Text('Change'),
          ),
        ],
      ),
    ) ?? false;

    if (!result) return;

    // Get the passcode as bytes
    String passcodeStr = passcodeController.text;
    List<int> passcodeBytes = [];

    // Convert each digit to a byte
    for (int i = 0; i < 6; i++) {
      int digit = int.parse(passcodeStr[i]);
      passcodeBytes.add(digit);
    }

    // Connect to device if not already connected
    if (device.connectionState != BluetoothConnectionState.connected) {
      await device.connectAndUpdateStream();
    }

    // Discover services
    List<BluetoothService> services = await device.discoverServices();

    // Find the control service and characteristic
    final serviceUuid = Guid("ffe0");
    final characteristicUuid = Guid("ffe5");

    BluetoothService? service = services.firstWhere(
      (s) => s.uuid == serviceUuid,
      orElse: () => throw Exception("Control service not found"),
    );

    BluetoothCharacteristic? characteristic = service.characteristics.firstWhere(
      (c) => c.uuid == characteristicUuid,
      orElse: () => throw Exception("Control characteristic not found"),
    );

    // Send passcode change command
    await characteristic.write(passcodeBytes);

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text('Passcode changed successfully'))
    );
  } catch (e) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text('Failed to change passcode: $e'))
    );
    print('Error changing passcode: $e');
  }
}

void _goToOTA(BuildContext context, BluetoothDevice device) async {
  try {
    // Show confirmation dialog
    bool confirm = await showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('Go to OTA Mode'),
        content: Text('Are you sure you want to put this device into OTA mode?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context, false),
            child: Text('Cancel'),
          ),
          TextButton(
            onPressed: () => Navigator.pop(context, true),
            style: TextButton.styleFrom(foregroundColor: Colors.orange),
            child: Text('OTA'),
          ),
        ],
      ),
    ) ?? false;

    if (!confirm) return;

    // Connect to device if not already connected
    if (device.connectionState != BluetoothConnectionState.connected) {
      await device.connectAndUpdateStream();
    }

    // Discover services
    List<BluetoothService> services = await device.discoverServices();

    // Find the OTA service and characteristic
    final serviceUuid = Guid("ffe0");
    final characteristicUuid = Guid("ffe3");

    BluetoothService? service = services.firstWhere(
      (s) => s.uuid == serviceUuid,
      orElse: () => throw Exception("OTA service not found"),
    );

    BluetoothCharacteristic? characteristic = service.characteristics.firstWhere(
      (c) => c.uuid == characteristicUuid,
      orElse: () => throw Exception("OTA characteristic not found"),
    );

    // Generate and send random byte
    final random = Random();
    List<int> randomByte = [random.nextInt(256)];
    await characteristic.write(randomByte);

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text('Device entered OTA mode successfully'))
    );
  } catch (e) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text('Failed to enter OTA mode: $e'))
    );
    print('Error entering OTA mode: $e');
  }
}
